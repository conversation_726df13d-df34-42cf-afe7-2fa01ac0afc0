import os
import base64
import io
import requests
from PIL import Image
from mapping_response import get_mapping_response_new
from tracker import add_to_success, add_to_failure
from save_classified_images import save_image_to_folder

url = "http://192.168.0.152:3005/predict"

def extract_text_from_image(image_path: str) -> str:
    """
    Extracts text from an image using a cropped section of the image.
    """
    try:
        image = Image.open(image_path)
        width, height = image.size

        # Cropping the top 1/4th of the image
        cropped_image = image.crop((0, 0, width, height // 4))

        # Convert image to base64
        buffer = io.BytesIO()
        cropped_image.save(buffer, format="PNG")
        buffer.seek(0)
        image_base64 = base64.b64encode(buffer.read()).decode('utf-8')

        prompt = """
            give me the heading and subheading of the table in the Image if it is present,
            just respond with table heading and subheading if it is present and 'none' if it is not present no explaination is needed
        """
        data = {"image_base64": image_base64, "prompt": prompt}
        response = requests.post(url, json=data)
        
        response_text = response.json().get('response', 'none').lower().replace(' ', '').replace('\n', '').strip()
        return get_mapping_response_new(response_text)
    
    except Exception as e:
        print(f"Error processing {image_path}: {e}")
        return 'error'

def classify_images(image_folder,output_dir):
    classified_data = {}
    try:

        for root, _, files in os.walk(image_folder):
            for file_name in files:
                if file_name.endswith(".png"):
                    image_path = os.path.join(root, file_name)
                    category = extract_text_from_image(image_path)

                    if category != 'error':
                        classified_data[image_path] = category
                        add_to_success(image_path, 'image-classification')
                        save_image_to_folder(image_path, category, output_dir)
                    else:
                        add_to_failure(image_path, 'image-classification')

        return classified_data
    except Exception as e :
        print(f"Error while classifing the images:{e}")
        return 'error'
