from pdf2image import convert_from_path
import os
from tracker import add_to_success,get_all_success_processed,add_to_failure
import mimetypes
import zipfile

def extract_zip(zip_path, extract_to_folder="extracted_folder"):
    
    os.makedirs(extract_to_folder, exist_ok=True)
    with zipfile.ZipFile(zip_path, 'r') as zip_ref:
        zip_ref.extractall(extract_to_folder)
    return os.path.abspath(extract_to_folder)

def convert_pdf_to_images(zip_path,output_img_folder):
    pdf_folder_path = extract_zip(zip_path)

    for root, _, files in os.walk(pdf_folder_path):
        if "__MACOSX" in _:
            _.remove("__MACOSX")
        try:
            for file_name in files:
                file_path = os.path.join(root, file_name)
                processed = get_all_success_processed(task='pdf-to-image-cfpp')
                if file_path not in processed:
                    add_to_success(url=file_path,task='pdf-to-image-cfpp')
                    # Process only PDF files
                    if file_name.endswith(".pdf") and mimetypes.guess_type(file_path)[0] == "application/pdf":
                        relative_path = os.path.relpath(root,pdf_folder_path)
 
                        # Define output paths while preserving structure
                        pdf_output_dir = os.path.join(
                            output_img_folder, relative_path
                        )
 
                        os.makedirs(pdf_output_dir, exist_ok=True)
 
                        try:
                            # Convert PDF to images
                            print(f"Processing {file_name}")
                            images = convert_from_path(file_path, dpi=150)
                            for i, page in enumerate(images):
                                image_path = os.path.join(pdf_output_dir, f"{os.path.splitext(file_name)[0]}_page_{i + 1}.png")
                                page.save(image_path, "PNG")
                        except Exception as e:
                            print(f"Failed to convert PDF to images: \n {file_path}")
                            add_to_failure(url=file_path,task='pdf-to-image-cfpp')
                            continue
                    
        except:
            continue
 

# zip_path = "/Users/<USER>/Documents/classification/SAIL.zip"
# output_img_folder = "/Users/<USER>/Documents/classification/Extracted_imag_from_pdf"

# convert_pdf_to_images(output_img_folder)


