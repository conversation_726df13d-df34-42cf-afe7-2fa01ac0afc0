from pdf2image import convert_from_path
import os
from tracker import add_to_success,get_all_success_processed,add_to_failure
import mimetypes
import zipfile

def extract_zip(zip_path, extract_to_folder=None):
    import tempfile

    if extract_to_folder is None:
        # Create a unique temporary directory for each extraction
        extract_to_folder = tempfile.mkdtemp(prefix="extracted_")

    os.makedirs(extract_to_folder, exist_ok=True)
    with zipfile.ZipFile(zip_path, 'r') as zip_ref:
        zip_ref.extractall(extract_to_folder)
    return os.path.abspath(extract_to_folder)

def convert_pdf_to_images(input_path, output_img_folder):
    """
    Convert PDF files to images from either a zip file or a directory.

    Args:
        input_path: Path to either a zip file containing PDFs or a directory with PDFs
        output_img_folder: Directory where converted images will be saved
    """
    # Check if the input is a zip file or a directory
    if os.path.isfile(input_path) and input_path.endswith('.zip'):
        # It's a zip file, extract it
        pdf_folder_path = extract_zip(input_path)
    elif os.path.isdir(input_path):
        # It's already a directory, use it directly
        pdf_folder_path = input_path
    else:
        raise ValueError(f"Input path '{input_path}' is neither a valid zip file nor a directory")

    for root, _, files in os.walk(pdf_folder_path):
        if "__MACOSX" in _:
            _.remove("__MACOSX")
        try:
            for file_name in files:
                file_path = os.path.join(root, file_name)
                processed = get_all_success_processed(task='pdf-to-image-cfpp')
                if file_path not in processed:
                    add_to_success(url=file_path,task='pdf-to-image-cfpp')
                    # Process only PDF files
                    if file_name.endswith(".pdf") and mimetypes.guess_type(file_path)[0] == "application/pdf":
                        relative_path = os.path.relpath(root,pdf_folder_path)

                        # Define output paths while preserving structure
                        pdf_output_dir = os.path.join(
                            output_img_folder, relative_path
                        )

                        os.makedirs(pdf_output_dir, exist_ok=True)

                        try:
                            # Convert PDF to images
                            print(f"Processing {file_name}")
                            images = convert_from_path(file_path, dpi=150)
                            for i, page in enumerate(images):
                                image_path = os.path.join(pdf_output_dir, f"{os.path.splitext(file_name)[0]}_page_{i + 1}.png")
                                page.save(image_path, "PNG")
                        except Exception as e:
                            print(f"Failed to convert PDF to images: \n {file_path}")
                            add_to_failure(url=file_path,task='pdf-to-image-cfpp')
                            continue

        except:
            continue


# zip_path = "/Users/<USER>/Documents/classification/SAIL.zip"
# output_img_folder = "/Users/<USER>/Documents/classification/Extracted_imag_from_pdf"

# convert_pdf_to_images(output_img_folder)


