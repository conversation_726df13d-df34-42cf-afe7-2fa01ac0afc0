def get_mapping_response_new(response):
    if 'consolidated' not in response:
        if 'balancesheet' in response and 'asat' in response and 'items' not in response and 'summarised' not in response and 'notes' not in response and 'ratios' not in response and 'item' not in response:
            return 'balancesheet'
        if ('ended' in response and 'statement'  in response and 'notes' not in response and 'items' not in response and 'summarised' not in response) and ('profitandloss' in response or 'income' in response):
            return 'profitandloss'
        if 'ended' in response and 'statement'  in response and 'notes' not in response and 'items' not in response and 'summarised' not in response and 'cashflow' in response :
            return 'cashflow'
    return 'none'