
import os
import boto3
import pandas as pd
from openpyxl.utils import get_column_letter
from openpyxl.styles import Border, Side, Alignment, PatternFill
import time
# AWS Textract client
textract = boto3.client("textract")

def clean_cell_content(content):
    """Clean and standardize cell content."""
    if content is None:
        return ''
    content = str(content).strip()
    # Replace various forms of dashes/hyphens with a standard one
    content = content.replace('—', '-').replace('–', '-')
    # Remove multiple spaces
    content = ' '.join(content.split())
    return content

def is_number(text):
    """Check if text represents a number (including decimals and negatives)."""
    try:
        float(text.replace(',', ''))
        return True
    except ValueError:
        return False

def extract_table_from_image(image_path):
    """Extract table data with improved accuracy from an image using AWS Textract."""
    textract = boto3.client("textract")
    
    with open(image_path, "rb") as img_file:
        response = textract.analyze_document(
            Document={'Bytes': img_file.read()},
            FeatureTypes=['TABLES']
        )
    
    time.sleep(2)

    tables = []
    blocks = response['Blocks']
    
    for block in blocks:
        if block['BlockType'] == "TABLE":
            table_cells = {}
            merged_cells = []
            
            # Get only table-related cells
            table_cells_blocks = [
                b for b in blocks 
                if b['BlockType'] == "CELL" and 'RowIndex' in b and 'ColumnIndex' in b
            ]
            
            if not table_cells_blocks:
                continue
                
            max_row = max(cell['RowIndex'] for cell in table_cells_blocks)
            max_col = max(cell['ColumnIndex'] for cell in table_cells_blocks)
            
            # Initialize table with empty strings
            table_data = [["" for _ in range(max_col)] for _ in range(max_row)]
            
            # Process each cell
            for cell in table_cells_blocks:
                row_idx = cell['RowIndex'] - 1
                col_idx = cell['ColumnIndex'] - 1
                
                # Get cell content
                cell_content = ""
                if 'Relationships' in cell:
                    for relationship in cell['Relationships']:
                        if relationship['Type'] == 'CHILD':
                            words = []
                            for child_id in relationship['Ids']:
                                child_block = next((b for b in blocks if b['Id'] == child_id), None)
                                if child_block and child_block['BlockType'] == 'WORD':
                                    words.append(child_block['Text'])
                            cell_content = ' '.join(words)
                
                # Clean cell content
                cell_content = clean_cell_content(cell_content)
                
                # Ignore cells with non-table information
                if cell_content.lower() in ["place", "date", "signature", "new delhi"]:
                    continue
                
                # Convert numbers to proper format
                if cell_content and is_number(cell_content.replace(',', '')):
                    try:
                        value = float(cell_content.replace(',', ''))
                        cell_content = f"{value:,.2f}" if '.' in cell_content else f"{int(value):,}"
                    except ValueError:
                        pass
                
                table_data[row_idx][col_idx] = cell_content
            
            # Remove empty rows
            processed_table = [row for row in table_data if any(cell.strip() for cell in row)]
            
            if processed_table:
                tables.append({
                    'data': processed_table,
                    'merged_cells': merged_cells
                })
    
    return tables

def apply_table_formatting(worksheet, table_info, start_row=1):
    """Apply formatting including borders and merged cells to the worksheet."""
    thin_border = Side(style='thin')
    border = Border(left=thin_border, right=thin_border, top=thin_border, bottom=thin_border)
    
    # Apply borders and alignment
    for row in worksheet.rows:
        for cell in row:
            cell.border = border
            cell.alignment = Alignment(horizontal='left', vertical='center', wrap_text=True)
            
            # Right-align numbers
            if cell.value and isinstance(cell.value, str) and is_number(cell.value):
                cell.alignment = Alignment(horizontal='right', vertical='center')
    
    # Apply merged cells
    for merge_info in table_info['merged_cells']:
        start_row_idx = merge_info['start_row'] + start_row
        start_col_idx = merge_info['start_col'] + 1
        end_row_idx = start_row_idx + merge_info['row_span'] - 1
        end_col_idx = start_col_idx + merge_info['col_span'] - 1
        
        merge_range = f"{get_column_letter(start_col_idx)}{start_row_idx}:{get_column_letter(end_col_idx)}{end_row_idx}"
        worksheet.merge_cells(merge_range)


def process_directory(directory_path):
    """Process all images in a directory and create a single Excel file with multiple sheets."""
    print(f"Processing directory: {directory_path}")
    
    image_files = [f for f in os.listdir(directory_path) 
                  if f.lower().endswith((".png", ".jpg", ".jpeg", ".tiff", ".bmp", ".pdf"))]
    
    if not image_files:
        return
    
    excel_path = os.path.join(directory_path, "results.xlsx")
    
    try:
        with pd.ExcelWriter(excel_path, engine='openpyxl') as writer:
            processed_tables = set()  # Track unique tables
            sheet_counter = 1
            
            for file in image_files:
                image_path = os.path.join(directory_path, file)
                print(f"Processing: {image_path}")
                
                try:
                    tables = extract_table_from_image(image_path)
                    
                    for table_info in tables:
                        # Convert table data to string for comparison
                        table_str = str(table_info['data'])
                        
                        # Skip if we've already processed this table
                        if table_str in processed_tables:
                            continue
                        
                        processed_tables.add(table_str)
                        
                        df = pd.DataFrame(table_info['data'])
                        print(df)
                        sheet_name = f"Sheet_{sheet_counter}"
                        
                        # Write to Excel
                        df.to_excel(writer, sheet_name=sheet_name, index=False, header=False)
                        
                        # Apply formatting
                        worksheet = writer.sheets[sheet_name]
                        apply_table_formatting(worksheet, table_info)
                        
                        # Auto-adjust columns
                        for idx, col in enumerate(df.columns):
                            max_length = max(df[col].astype(str).str.len().max(), len(str(col))) + 2
                            worksheet.column_dimensions[get_column_letter(idx + 1)].width = min(max_length, 50)
                        
                        sheet_counter += 1
                
                except Exception as e:
                    print(f"Error processing {image_path}: {str(e)}")
        
        print(f"Saved results to: {excel_path}")
    
    except Exception as e:
        print(f"Error creating Excel file: {str(e)}")

def process_images(base_dir):
    """Traverse through directories and process images."""
    for root, _, _ in os.walk(base_dir):
        if any(f.lower().endswith((".png", ".jpg", ".jpeg", ".tiff", ".bmp", ".pdf")) 
               for f in os.listdir(root)):
            process_directory(root)

# # Run the process
# if __name__ == "__main__":
# BASE_DIR = "/Users/<USER>/Documents/classification/Archive_classified_with_tables"
# process_images(BASE_DIR)

# image_path = f"/Users/<USER>/Documents/classification/Classified_Images/SAIL2/2023/cashflow/Annual%20Report%202022-23_page_79.png"  
# table_dataframes = extract_table_from_image(image_path)

# for idx, df in enumerate(table_dataframes):
#     print(f"\nTable {idx + 1}:")
#     print(df)


# import os
# import boto3
# import pandas as pd
# from openpyxl.utils import get_column_letter
# from openpyxl.styles import Border, Side, Alignment
# import time
# from trp import Document
# import re
# from dotenv import load_dotenv

# # Load AWS credentials and initialize the Textract client
# load_dotenv()
# textract = boto3.client(
#     'textract',
#     aws_access_key_id=os.getenv("AWS_ACCESS_KEY_ID"),
#     aws_secret_access_key=os.getenv("AWS_SECRET_ACCESS_KEY"),
#     region_name=os.getenv("AWS_REGION")
# )

# # Clean content helper function
# def clean_cell_content(content):
#     """Clean and standardize cell content."""
#     if content is None:
#         return ''
#     content = str(content).strip()
#     content = content.replace('—', '-').replace('–', '-')
#     content = ' '.join(content.split())
#     return content

# # Function to process each page and table using trp Document
# def extract_tables_from_textract_response(response):
#     """Extract tables from Textract response and return as a list of DataFrames."""
#     doc = Document(response)
#     tables_dataframes = []
    
#     for page in doc.pages:
#         for table in page.tables:
#             table_data = []
#             for row in table.rows:
#                 row_data = [cell.text.strip() for cell in row.cells]
#                 table_data.append(row_data)
#             df = pd.DataFrame(table_data)
#             tables_dataframes.append(df)
    
#     return tables_dataframes

# # Function to save DataFrame to Excel with table formatting
# def save_to_excel_with_formatting(df, excel_file_path, sheet_name):
#     """Save DataFrame to Excel and apply formatting."""
#     with pd.ExcelWriter(excel_file_path, engine='openpyxl', mode='a', if_sheet_exists='overlay') as writer:
#         df.to_excel(writer, sheet_name=sheet_name, index=False, header=False)
#         workbook = writer.book
#         worksheet = workbook[sheet_name]
        
#         # Define border styles
#         thin_border = Border(
#             left=Side(style='thin'),
#             right=Side(style='thin'),
#             top=Side(style='thin'),
#             bottom=Side(style='thin')
#         )
        
#         # Apply borders to each cell
#         for row in worksheet.iter_rows(min_row=2, max_row=worksheet.max_row, min_col=1, max_col=worksheet.max_column):
#             for cell in row:
#                 cell.border = thin_border

# # Function to process a single file
# def process_image(image_path, excel_file_path):
#     """Process a single image, extract tables using Textract, and save them to Excel."""
#     try:
#         with open(image_path, "rb") as img_file:
#             response = textract.analyze_document(
#                 Document={'Bytes': img_file.read()},
#                 FeatureTypes=["TABLES"]
#             )
        
#         tables = extract_tables_from_textract_response(response)
#         for idx, df in enumerate(tables):
#             sheet_name = f"Table_{idx + 1}"
#             save_to_excel_with_formatting(df, excel_file_path, sheet_name)
#             print(f"Saved table {idx + 1} from {image_path} to Excel.")
    
#     except Exception as e:
#         print(f"Error processing image {image_path}: {str(e)}")

# # Function to process a directory of images
# def process_directory(directory_path):
#     """Process all images in a directory and create a single Excel file."""
#     image_files = [f for f in os.listdir(directory_path) if f.lower().endswith((".png", ".jpg", ".jpeg", ".tiff", ".bmp", ".pdf"))]
    
#     if not image_files:
#         return
    
#     excel_path = os.path.join(directory_path, "results.xlsx")
    
#     # Initialize Excel writer
#     try:
#         for file in image_files:
#             image_path = os.path.join(directory_path, file)
#             print(f"Processing {image_path}...")
#             process_image(image_path, excel_path)
        
#         print(f"Saved results to: {excel_path}")
    
#     except Exception as e:
#         print(f"Error creating Excel file: {str(e)}")

# # Run the process
# if __name__ == "__main__":
#     BASE_DIR = "/path/to/your/images"
#     process_directory(BASE_DIR)


