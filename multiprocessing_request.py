import multiprocessing
import os
from main import process_zip_and_classify_with_tables

def run_in_process(zip_file: str, result_queue: multiprocessing.Queue,unique_id:str):

    # Call the main function
    output_file = process_zip_and_classify_with_tables(zip_file,unique_id)
    
    # Put the output file path into the queue to be accessed by the main process
    result_queue.put(output_file)

def main_process(zip_file,unique_id):
    # Create a queue to communicate with the child process
    result_queue = multiprocessing.Queue()

    # Start the process
    process = multiprocessing.Process(target=run_in_process, args=(zip_file, result_queue,unique_id))
    process.start()

    # Wait for the process to finish
    process.join()
 
    # Retrieve the output file path from the queue
    output_file_path = result_queue.get()

    # Return the path to the processed file
    return output_file_path
