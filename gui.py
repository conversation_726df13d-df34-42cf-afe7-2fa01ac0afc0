import streamlit as st
from multiprocessing_request import main_process
import os
import zipfile
import uuid
import shutil


def main():
    st.title("Classification of Images")

    # File Upload section
    st.subheader("Upload the main zip file")
    uploaded_file = st.file_uploader('Upload', type='zip')

    # Ensure the Submit button is visible once a file is uploaded
    if uploaded_file is not None:     
        # Show the Submit button
        submit_button = st.button("Submit")

        if submit_button:
            with st.spinner('Processing the zip file...'):
                # Process the uploaded zip file and wait for the output
                            # Show a loader while processing the files
                    unique_id = str(uuid.uuid4())
                    temp_dir = f"temp_processed_{unique_id}_files"
                    if not os.path.exists(temp_dir):
                        os.makedirs(temp_dir)

                    # Extract files from the uploaded zip
                    with zipfile.ZipFile(uploaded_file, 'r') as zip_ref:
                        zip_ref.extractall(temp_dir)
                        output_zip = main_process(temp_dir,unique_id)
                        print(output_zip)

            # After processing, provide a download button for the user
            # Ensure the file is ready before showing the button
            if output_zip:
                with open(output_zip, "rb") as f:
                    st.download_button(
                        label="Download classified images with excels",
                        data=f,
                        file_name=output_zip,
                        mime="application/zip"
                    )
                shutil.rmtree(temp_dir)
                os.remove(output_zip)
            else:
                st.error("Failed to process the zip file. Please try again.")
        else:
            st.info("Please upload a zip file ")

if __name__ == "__main__":
    main()
