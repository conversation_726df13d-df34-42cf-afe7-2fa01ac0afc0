import os

def delete_results_files(base_dir):

    deleted_count = 0
    
    for root, _, files in os.walk(base_dir):
        if 'results.xlsx' in files:
            file_path = os.path.join(root, 'results.xlsx')
            try:
                os.remove(file_path)
                print(f"Deleted: {file_path}")
                deleted_count += 1
            except Exception as e:
                print(f"Error deleting {file_path}: {str(e)}")
    
    print(f"\nTotal files deleted: {deleted_count}")

base_dir= "/Users/<USER>/Documents/classification/Archive_classified_with_tables"
    

delete_results_files(base_dir)