import torch
import os
# from transformers import AutoModel, AutoTokenizer
# from langchain.chat_models import ChatOpenAI
from langchain_community.chat_models import ChatOpenAI
from langchain_openai import ChatOpenAI


device = torch.device('cuda:0')  

os.environ["OPENAI_API_KEY"] = "dummy_api_key"
os.environ["OPENAI_API_BASE"] = "http://192.168.0.152:3005/predict"

# # Load model and tokenizer and move model to the specified GPU
# minicpm_model = AutoModel.from_pretrained('openbmb/MiniCPM-V-2_6-int4', trust_remote_code=True)
# minicpm_tokenizer = AutoTokenizer.from_pretrained('openbmb/MiniCPM-V-2_6-int4', trust_remote_code=True)

llm = ChatOpenAI(
    model="Qwen/Qwen2.5-Coder-7B-Instruct-GPTQ-Int8",
    temperature=0.1,
    seed = 42
)

