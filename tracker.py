import os
import pickle as pkl

if not os.path.exists('pickle_files'):
    os.makedirs('pickle_files')


def add_to_success(url,task):
    file = task+'.pkl'
    all_success_scraped = []
    if os.path.isfile(f'pickle_files/{file}'):
        all_success_scraped = pkl.load(open(f'pickle_files/{file}','rb'))
    all_success_scraped.append(url)
    with open(f'pickle_files/{file}', 'wb') as f:
        pkl.dump(all_success_scraped, f)

def get_all_success_processed(task):
    file = task + '.pkl'
    all_success_scraped = []
    if os.path.isfile(f'pickle_files/{file}'):
        all_success_scraped = pkl.load(open(f'pickle_files/{file}','rb'))
    return all_success_scraped


def add_to_failure(url,task):
    file = task+'_failed_.pkl'
    all_failed_scraped = []
    if os.path.isfile(f'pickle_files/{file}'):
        all_failed_scraped = pkl.load(open(f'pickle_files/{file}','rb'))
    all_failed_scraped.append(url)
    with open(f'pickle_files/{file}', 'wb') as f:
        pkl.dump(all_failed_scraped, f)