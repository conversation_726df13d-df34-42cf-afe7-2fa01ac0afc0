{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Collecting amazon-textract-textractor\n", "  Downloading amazon_textract_textractor-1.8.5-py3-none-any.whl.metadata (9.7 kB)\n", "Requirement already satisfied: Pillow in /Users/<USER>/miniconda3/lib/python3.12/site-packages (from amazon-textract-textractor) (11.1.0)\n", "Collecting XlsxWriter<4,>=3.0 (from amazon-textract-textractor)\n", "  Downloading XlsxWriter-3.2.2-py3-none-any.whl.metadata (2.8 kB)\n", "Requirement already satisfied: amazon-textract-caller<1,>=0.2.4 in /Users/<USER>/miniconda3/lib/python3.12/site-packages (from amazon-textract-textractor) (0.2.4)\n", "Collecting editdistance<0.9,>=0.6.2 (from amazon-textract-textractor)\n", "  Downloading editdistance-0.8.1-cp312-cp312-macosx_11_0_arm64.whl.metadata (3.9 kB)\n", "Requirement already satisfied: tabulate<0.10,>=0.9 in /Users/<USER>/miniconda3/lib/python3.12/site-packages (from amazon-textract-textractor) (0.9.0)\n", "Requirement already satisfied: boto3>=1.26.35 in /Users/<USER>/miniconda3/lib/python3.12/site-packages (from amazon-textract-caller<1,>=0.2.4->amazon-textract-textractor) (1.36.23)\n", "Requirement already satisfied: botocore in /Users/<USER>/miniconda3/lib/python3.12/site-packages (from amazon-textract-caller<1,>=0.2.4->amazon-textract-textractor) (1.36.23)\n", "Requirement already satisfied: amazon-textract-response-parser>=0.1.39 in /Users/<USER>/miniconda3/lib/python3.12/site-packages (from amazon-textract-caller<1,>=0.2.4->amazon-textract-textractor) (1.0.3)\n", "Requirement already satisfied: marshmallow<4,>=3.14 in /Users/<USER>/miniconda3/lib/python3.12/site-packages (from amazon-textract-response-parser>=0.1.39->amazon-textract-caller<1,>=0.2.4->amazon-textract-textractor) (3.26.1)\n", "Requirement already satisfied: jmespath<2.0.0,>=0.7.1 in /Users/<USER>/miniconda3/lib/python3.12/site-packages (from boto3>=1.26.35->amazon-textract-caller<1,>=0.2.4->amazon-textract-textractor) (1.0.1)\n", "Requirement already satisfied: s3transfer<0.12.0,>=0.11.0 in /Users/<USER>/miniconda3/lib/python3.12/site-packages (from boto3>=1.26.35->amazon-textract-caller<1,>=0.2.4->amazon-textract-textractor) (0.11.2)\n", "Requirement already satisfied: python-dateutil<3.0.0,>=2.1 in /Users/<USER>/miniconda3/lib/python3.12/site-packages (from botocore->amazon-textract-caller<1,>=0.2.4->amazon-textract-textractor) (2.9.0.post0)\n", "Requirement already satisfied: urllib3!=2.2.0,<3,>=1.25.4 in /Users/<USER>/miniconda3/lib/python3.12/site-packages (from botocore->amazon-textract-caller<1,>=0.2.4->amazon-textract-textractor) (2.3.0)\n", "Requirement already satisfied: packaging>=17.0 in /Users/<USER>/miniconda3/lib/python3.12/site-packages (from marshmallow<4,>=3.14->amazon-textract-response-parser>=0.1.39->amazon-textract-caller<1,>=0.2.4->amazon-textract-textractor) (24.2)\n", "Requirement already satisfied: six>=1.5 in /Users/<USER>/miniconda3/lib/python3.12/site-packages (from python-dateutil<3.0.0,>=2.1->botocore->amazon-textract-caller<1,>=0.2.4->amazon-textract-textractor) (1.17.0)\n", "Downloading amazon_textract_textractor-1.8.5-py3-none-any.whl (309 kB)\n", "Downloading editdistance-0.8.1-cp312-cp312-macosx_11_0_arm64.whl (79 kB)\n", "Downloading XlsxWriter-3.2.2-py3-none-any.whl (165 kB)\n", "Installing collected packages: XlsxWriter, editdistance, amazon-textract-textractor\n", "Successfully installed XlsxWriter-3.2.2 amazon-textract-textractor-1.8.5 editdistance-0.8.1\n", "Note: you may need to restart the kernel to use updated packages.\n"]}], "source": ["pip install amazon-textract-textractor"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Collecting amazon-textract-caller\n", "  Downloading amazon_textract_caller-0.2.4-py2.py3-none-any.whl.metadata (7.2 kB)\n", "Requirement already satisfied: boto3>=1.26.35 in /Users/<USER>/miniconda3/lib/python3.12/site-packages (from amazon-textract-caller) (1.36.23)\n", "Requirement already satisfied: botocore in /Users/<USER>/miniconda3/lib/python3.12/site-packages (from amazon-textract-caller) (1.36.23)\n", "Collecting amazon-textract-response-parser>=0.1.39 (from amazon-textract-caller)\n", "  Downloading amazon_textract_response_parser-1.0.3-py2.py3-none-any.whl.metadata (11 kB)\n", "Requirement already satisfied: marshmallow<4,>=3.14 in /Users/<USER>/miniconda3/lib/python3.12/site-packages (from amazon-textract-response-parser>=0.1.39->amazon-textract-caller) (3.26.1)\n", "Requirement already satisfied: jmespath<2.0.0,>=0.7.1 in /Users/<USER>/miniconda3/lib/python3.12/site-packages (from boto3>=1.26.35->amazon-textract-caller) (1.0.1)\n", "Requirement already satisfied: s3transfer<0.12.0,>=0.11.0 in /Users/<USER>/miniconda3/lib/python3.12/site-packages (from boto3>=1.26.35->amazon-textract-caller) (0.11.2)\n", "Requirement already satisfied: python-dateutil<3.0.0,>=2.1 in /Users/<USER>/miniconda3/lib/python3.12/site-packages (from botocore->amazon-textract-caller) (2.9.0.post0)\n", "Requirement already satisfied: urllib3!=2.2.0,<3,>=1.25.4 in /Users/<USER>/miniconda3/lib/python3.12/site-packages (from botocore->amazon-textract-caller) (2.3.0)\n", "Requirement already satisfied: packaging>=17.0 in /Users/<USER>/miniconda3/lib/python3.12/site-packages (from marshmallow<4,>=3.14->amazon-textract-response-parser>=0.1.39->amazon-textract-caller) (24.2)\n", "Requirement already satisfied: six>=1.5 in /Users/<USER>/miniconda3/lib/python3.12/site-packages (from python-dateutil<3.0.0,>=2.1->botocore->amazon-textract-caller) (1.17.0)\n", "Downloading amazon_textract_caller-0.2.4-py2.py3-none-any.whl (13 kB)\n", "Downloading amazon_textract_response_parser-1.0.3-py2.py3-none-any.whl (30 kB)\n", "Installing collected packages: amazon-textract-response-parser, amazon-textract-caller\n", "Successfully installed amazon-textract-caller-0.2.4 amazon-textract-response-parser-1.0.3\n", "Note: you may need to restart the kernel to use updated packages.\n"]}], "source": ["pip install amazon-textract-caller"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Collecting pypdf\n", "  Downloading pypdf-5.3.1-py3-none-any.whl.metadata (7.3 kB)\n", "Downloading pypdf-5.3.1-py3-none-any.whl (302 kB)\n", "Installing collected packages: pypdf\n", "Successfully installed pypdf-5.3.1\n", "Note: you may need to restart the kernel to use updated packages.\n"]}], "source": ["pip install pypdf"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"ename": "ClientError", "evalue": "An error occurred (UnrecognizedClientException) when calling the DetectDocumentText operation: The security token included in the request is invalid.", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mClientError\u001b[0m                               Trace<PERSON> (most recent call last)", "Cell \u001b[0;32mIn[3], line 4\u001b[0m\n\u001b[1;32m      1\u001b[0m \u001b[38;5;28;01m<PERSON>rom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01mlangchain_community\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mdocument_loaders\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m AmazonTextractPDFLoader\n\u001b[1;32m      3\u001b[0m loader \u001b[38;5;241m=\u001b[39m AmazonTextractPDFLoader(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m/Users/<USER>/Downloads/AI_customization_interface.jpg\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[0;32m----> 4\u001b[0m documents \u001b[38;5;241m=\u001b[39m \u001b[43mloader\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mload\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/miniconda3/lib/python3.12/site-packages/langchain_community/document_loaders/pdf.py:1064\u001b[0m, in \u001b[0;36mAmazonTextractPDFLoader.load\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m   1062\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21mload\u001b[39m(\u001b[38;5;28mself\u001b[39m) \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m>\u001b[39m \u001b[38;5;28mlist\u001b[39m[Document]:\n\u001b[1;32m   1063\u001b[0m \u001b[38;5;250m    \u001b[39m\u001b[38;5;124;03m\"\"\"Load given path as pages.\"\"\"\u001b[39;00m\n\u001b[0;32m-> 1064\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mlist\u001b[39;49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mlazy_load\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/miniconda3/lib/python3.12/site-packages/langchain_community/document_loaders/pdf.py:1085\u001b[0m, in \u001b[0;36mAmazonTextractPDFLoader.lazy_load\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m   1078\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m AmazonTextractPDFLoader\u001b[38;5;241m.\u001b[39m_get_number_of_pages(blob) \u001b[38;5;241m>\u001b[39m \u001b[38;5;241m1\u001b[39m:\n\u001b[1;32m   1079\u001b[0m         \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mValueError\u001b[39;00m(\n\u001b[1;32m   1080\u001b[0m             \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mthe file \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mblob\u001b[38;5;241m.\u001b[39mpath\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m is a multi-page document, \u001b[39m\u001b[38;5;130;01m\\\u001b[39;00m\n\u001b[1;32m   1081\u001b[0m \u001b[38;5;124m            but not stored on S3. \u001b[39m\u001b[38;5;130;01m\\\u001b[39;00m\n\u001b[1;32m   1082\u001b[0m \u001b[38;5;124m            Textract requires multi-page documents to be on S3.\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m   1083\u001b[0m         )\n\u001b[0;32m-> 1085\u001b[0m \u001b[38;5;28;01myield from\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mparser\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mparse\u001b[49m\u001b[43m(\u001b[49m\u001b[43mblob\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/miniconda3/lib/python3.12/site-packages/langchain_core/document_loaders/base.py:126\u001b[0m, in \u001b[0;36mBaseBlobParser.parse\u001b[0;34m(self, blob)\u001b[0m\n\u001b[1;32m    111\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21mparse\u001b[39m(\u001b[38;5;28mself\u001b[39m, blob: Blob) \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m>\u001b[39m \u001b[38;5;28mlist\u001b[39m[Document]:\n\u001b[1;32m    112\u001b[0m \u001b[38;5;250m    \u001b[39m\u001b[38;5;124;03m\"\"\"Eagerly parse the blob into a document or documents.\u001b[39;00m\n\u001b[1;32m    113\u001b[0m \n\u001b[1;32m    114\u001b[0m \u001b[38;5;124;03m    This is a convenience method for interactive development environment.\u001b[39;00m\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    124\u001b[0m \u001b[38;5;124;03m        List of documents\u001b[39;00m\n\u001b[1;32m    125\u001b[0m \u001b[38;5;124;03m    \"\"\"\u001b[39;00m\n\u001b[0;32m--> 126\u001b[0m     \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m \u001b[38;5;28;43mlist\u001b[39;49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mlazy_parse\u001b[49m\u001b[43m(\u001b[49m\u001b[43mblob\u001b[49m\u001b[43m)\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/miniconda3/lib/python3.12/site-packages/langchain_community/document_loaders/parsers/pdf.py:1433\u001b[0m, in \u001b[0;**************************.lazy_parse\u001b[0;34m(self, blob)\u001b[0m\n\u001b[1;32m   1427\u001b[0m     textract_response_json \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mtc\u001b[38;5;241m.\u001b[39mcall_textract(\n\u001b[1;32m   1428\u001b[0m         input_document\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mstr\u001b[39m(blob\u001b[38;5;241m.\u001b[39mpath),  \u001b[38;5;66;03m# type: ignore[attr-defined]\u001b[39;00m\n\u001b[1;32m   1429\u001b[0m         features\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mtextract_features,\n\u001b[1;32m   1430\u001b[0m         boto3_textract_client\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mboto3_textract_client,\n\u001b[1;32m   1431\u001b[0m     )\n\u001b[1;32m   1432\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m-> 1433\u001b[0m     textract_response_json \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mtc\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mcall_textract\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m   1434\u001b[0m \u001b[43m        \u001b[49m\u001b[43minput_document\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mblob\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mas_bytes\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\u001b[43m  \u001b[49m\u001b[38;5;66;43;03m# type: ignore[attr-defined]\u001b[39;49;00m\n\u001b[1;32m   1435\u001b[0m \u001b[43m        \u001b[49m\u001b[43mfeatures\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mtextract_features\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1436\u001b[0m \u001b[43m        \u001b[49m\u001b[43mcall_mode\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mtc\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mTextract_Call_Mode\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mFORCE_SYNC\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1437\u001b[0m \u001b[43m        \u001b[49m\u001b[43mboto3_textract_client\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mboto3_textract_client\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1438\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1440\u001b[0m document \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mtextractor\u001b[38;5;241m.\u001b[39mDocument\u001b[38;5;241m.\u001b[39mopen(textract_response_json)\n\u001b[1;32m   1442\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m idx, page \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28menumerate\u001b[39m(document\u001b[38;5;241m.\u001b[39mpages):\n", "File \u001b[0;32m~/miniconda3/lib/python3.12/site-packages/textractcaller/t_call.py:693\u001b[0m, in \u001b[0;36mcall_textract\u001b[0;34m(input_document, features, queries_config, output_config, adapters_config, kms_key_id, job_tag, notification_channel, client_request_token, return_job_id, force_async_api, call_mode, boto3_textract_client, job_done_polling_interval, mime_type)\u001b[0m\n\u001b[1;32m    691\u001b[0m         result_value \u001b[38;5;241m=\u001b[39m textract\u001b[38;5;241m.\u001b[39manalyze_document(\u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mparams)\n\u001b[1;32m    692\u001b[0m     \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m--> 693\u001b[0m         result_value \u001b[38;5;241m=\u001b[39m \u001b[43mtextract\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mdetect_document_text\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mparams\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    694\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m    695\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mValueError\u001b[39;00m(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124munsupported input_document type: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[38;5;28mtype\u001b[39m(input_document)\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m)\n", "File \u001b[0;32m~/miniconda3/lib/python3.12/site-packages/botocore/client.py:569\u001b[0m, in \u001b[0;36mClientCreator._create_api_method.<locals>._api_call\u001b[0;34m(self, *args, **kwargs)\u001b[0m\n\u001b[1;32m    565\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mTypeError\u001b[39;00m(\n\u001b[1;32m    566\u001b[0m         \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mpy_operation_name\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m() only accepts keyword arguments.\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m    567\u001b[0m     )\n\u001b[1;32m    568\u001b[0m \u001b[38;5;66;03m# The \"self\" in this scope is referring to the BaseClient.\u001b[39;00m\n\u001b[0;32m--> 569\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_make_api_call\u001b[49m\u001b[43m(\u001b[49m\u001b[43moperation_name\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/miniconda3/lib/python3.12/site-packages/botocore/client.py:1023\u001b[0m, in \u001b[0;36mBaseClient._make_api_call\u001b[0;34m(self, operation_name, api_params)\u001b[0m\n\u001b[1;32m   1019\u001b[0m     error_code \u001b[38;5;241m=\u001b[39m error_info\u001b[38;5;241m.\u001b[39mget(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mQueryErrorCode\u001b[39m\u001b[38;5;124m\"\u001b[39m) \u001b[38;5;129;01mor\u001b[39;00m error_info\u001b[38;5;241m.\u001b[39mget(\n\u001b[1;32m   1020\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mCode\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m   1021\u001b[0m     )\n\u001b[1;32m   1022\u001b[0m     error_class \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mexceptions\u001b[38;5;241m.\u001b[39mfrom_code(error_code)\n\u001b[0;32m-> 1023\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m error_class(parsed_response, operation_name)\n\u001b[1;32m   1024\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m   1025\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m parsed_response\n", "\u001b[0;31mClientError\u001b[0m: An error occurred (UnrecognizedClientException) when calling the DetectDocumentText operation: The security token included in the request is invalid."]}], "source": ["from langchain_community.document_loaders import AmazonTextractPDFLoader\n", " \n", "loader = AmazonTextractPDFLoader(\"/Users/<USER>/Downloads/AI_customization_interface.jpg\")\n", "documents = loader.load()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 2}