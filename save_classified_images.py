import os
import shutil

def save_image_to_folder(image_path, classification, output_dir):
    """
    Copies the classified image to the appropriate folder based on classification.

    Args:
    - image_path (str): The path to the image file.
    - classification (str): The classification label (e.g., balancesheet, profitandloss, cashflow, unclassified).
    - output_dir (str): The directory where the classified images will be saved.
    """
    try:

        folder_mapping = {
            "balancesheet": "balancesheet",
            "profitandloss": "profitandloss",
            "cashflow": "cashflow",
            "incomestatement": "incomestatement"
        }

        # Skipping the files if the classification is not recognized
        if classification.lower() not in folder_mapping:
            print(f"Skipping unclassified image: {image_path}")
            return
        
        path_parts = image_path.split(os.sep)
        company_name = path_parts[-3] if len(path_parts) >= 3 else "Unknown_Company"
        year = path_parts[-2] if len(path_parts) >= 2 else "Unknown_Year"


        # Create the target folder if it doesn't exist
        target_path = os.path.join(output_dir,company_name,year ,folder_mapping[classification.lower()])
        os.makedirs(target_path, exist_ok=True)

        # Copy the image to the corresponding folder
        file_name = os.path.basename(image_path)
        destination = os.path.join(target_path, file_name)
        shutil.copy(image_path, destination)

        print(f"Image {file_name} classified as {classification} and copied to {target_path}/ folder.")
    
    except Exception as e:
        print(f"Error while saving the classified images :{e}")
        return 'error'
