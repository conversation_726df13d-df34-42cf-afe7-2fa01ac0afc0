# import boto3
# import json
# import io
# import pdfplumber
# from PyPDF2 import Pdf<PERSON><PERSON><PERSON>, PdfWriter
# from reportlab.pdfgen import canvas
# from reportlab.lib.pagesizes import letter
 
# # 🔐 Use your AWS credentials here if not using `aws configure`
# # aws_access_key = "ASIA4WDCWCDFDZU5B6RR"
# # aws_secret_key = "VgBPyfTIWForLm/vtrMt+lqpDACURjWiWTvrZyN+"
# # aws_region = "us-east-1"
 
# # Initialize AWS Textract client
# # session = boto3.Session(
# #     aws_access_key_id=aws_access_key,
# #     aws_secret_access_key=aws_secret_key,
# #     region_name=aws_region
# # )
# textract = boto3.client("textract",region_name = "us-east-1")
 
 
# def extract_input_fields_from_pdf(pdf_path):
#     """Extracts input fields from PDF using Amazon Textract"""
#     with open(pdf_path, "rb") as file:
#         pdf_bytes = file.read()
 
#     # Send to Amazon Textract
#     response = textract.analyze_document(
#         Document={"Bytes": pdf_bytes}, FeatureTypes=["FORMS"]
#     )
 
#     fields = {}
 
#     for block in response["Blocks"]:
#         if block["BlockType"] == "KEY_VALUE_SET" and "KEY" in block["EntityTypes"]:
#             key = ""
#             value = ""
 
#             for rel in block.get("Relationships", []):
#                 if rel["Type"] == "CHILD":
#                     for child_id in rel["Ids"]:
#                         child_block = next(
#                             b for b in response["Blocks"] if b["Id"] == child_id
#                         )
#                         if child_block["BlockType"] == "WORD":
#                             key += child_block["Text"] + " "
 
#             key = key.strip()
 
#             for rel in block.get("Relationships", []):
#                 if rel["Type"] == "VALUE":
#                     for child_id in rel["Ids"]:
#                         child_block = next(
#                             b for b in response["Blocks"] if b["Id"] == child_id
#                         )
#                         if child_block["BlockType"] == "WORD":
#                             value += child_block["Text"] + " "
 
#             value = value.strip()
 
#             fields[key] = value if value else "___"  # Placeholder for empty fields
 
#     return fields
 
 
# def fill_pdf(input_pdf_path, output_pdf_path, field_values):
#     """Fills the extracted fields in the PDF with the provided values"""
#     # Read the original PDF
#     pdf_reader = PdfReader(input_pdf_path)
#     pdf_writer = PdfWriter()
 
#     packet = io.BytesIO()
#     can = canvas.Canvas(packet, pagesize=letter)
 
#     # Extract text positions using pdfplumber
#     with pdfplumber.open(input_pdf_path) as pdf:
#         for i, page in enumerate(pdf.pages):
#             for key, value in field_values.items():
#                 words = page.extract_words()
#                 for word in words:
#                     if word["text"] == key:
#                         x, y = word["x0"], letter[1] - word["top"]
#                         can.drawString(x + 50, y, value)
 
#     can.save()
#     packet.seek(0)
 
#     # Merge overlay PDF with original PDF
#     overlay_pdf = PdfReader(packet)
#     for i in range(len(pdf_reader.pages)):
#         page = pdf_reader.pages[i]
#         page.merge_page(overlay_pdf.pages[i])
#         pdf_writer.add_page(page)
 
#     # Save final PDF
#     with open(output_pdf_path, "wb") as output_file:
#         pdf_writer.write(output_file)
 
 
# # --- Usage Example ---
 
# pdf_path = "3A.pdf"
# output_path = "filled_fw9.pdf"
 
# # Step 1: Extract fields
# fields = extract_input_fields_from_pdf(pdf_path)
# print("Extracted Fields: ", json.dumps(fields, indent=4))
 
# # Step 2: User provides input values
# user_inputs = {
#     "Name of entity/individual": "John Doe",
#     "Business name/disregarded entity name": "Doe Enterprises",
#     "Address": "123 Main St, New York, NY",
#     "City, state, and ZIP code": "New York, NY 10001",
#     "Social security number": "***********",
#     "Employer identification number": "98-7654321",
#     "Signature of U.S. person": "John Doe",
# }
 
# # Step 3: Fill the PDF with user input
# fill_pdf(pdf_path, output_path, user_inputs)
 
# print(f"✅ Filled PDF saved at: {output_path}")


