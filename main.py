import os
from extracting_images_from_pdf import convert_pdf_to_images
from Extract_text_classification import classify_images
from aws_textract import process_images
import zipfile
import shutil
import tempfile

def process_zip_and_classify_with_tables(input_zip_path, unique_id=None):
    temp_dir = tempfile.mkdtemp()
    output_img_folder = os.path.join(temp_dir, "Extracted_images")
    classified_output_dir = os.path.join(temp_dir, "Classified_Images")

    try:

        print("Step 1: Extracting PDFs and converting to images...")
        convert_pdf_to_images(input_zip_path, output_img_folder)

        print("Step 2: Classifying images...")
        classified_data = classify_images(output_img_folder, classified_output_dir)

        print("Step 3: Extracting tables and creating Excel files...")
        process_images(classified_output_dir)

        print("Step 4: Creating output zip file...")
        # Handle both zip files and directories for output naming
        if input_zip_path.endswith('.zip'):
            output_zip_path = os.path.splitext(input_zip_path)[0] + "_classified_with_tables.zip"
        else:
            # It's a directory, create output zip in the same parent directory
            output_zip_path = input_zip_path + "_classified_with_tables.zip"
        with zipfile.ZipFile(output_zip_path, 'w') as zipf:
            for root, _, files in os.walk(classified_output_dir):
                for file in files:
                    file_path = os.path.join(root, file)
                    rel_path = os.path.relpath(file_path, classified_output_dir)
                    zipf.write(file_path, rel_path)

        print("\nClassification Results:")
        for image_path, category in classified_data.items():
            print(f"{image_path} -> {category}")

        print(f"\nClassified images and tables have been saved to: {output_zip_path}")
        return output_zip_path

    finally:
        shutil.rmtree(temp_dir)

if __name__ == "__main__":
    import sys

    if len(sys.argv) > 1:
        # Use command line argument if provided
        zip_path = sys.argv[1]
    else:
        # Ask user for input if no command line argument
        zip_path = input("Enter the path to the input zip file: ")

    if not os.path.exists(zip_path):
        print(f"Error: File '{zip_path}' does not exist.")
        sys.exit(1)

    if not zip_path.endswith('.zip'):
        print(f"Error: '{zip_path}' is not a zip file.")
        sys.exit(1)

    print(f"Processing zip file: {zip_path}")
    try:
        result_zip = process_zip_and_classify_with_tables(zip_path)
        print(f"✅ Processing completed successfully!")
        print(f"📁 Output saved to: {result_zip}")
    except Exception as e:
        print(f"❌ Error during processing: {e}")
        sys.exit(1)
